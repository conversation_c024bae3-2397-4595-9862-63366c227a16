{"name": "@CDNA-Technologies/svelte-vitals", "version": "0.0.122", "sideEffects": false, "repository": {"type": "git", "url": "git+https://github.com/CDNA-Technologies/svelte-vitals.git"}, "homepage": "https://github.com/CDNA-Technologies/svelte-vitals#readme", "main": "./index.js", "module": "./index.js", "svelte": "./index.js", "types": "./index.d.ts", "exports": {"./package.json": "./package.json", ".": {"import": "./index.js", "types": "./index.d.ts", "svelte": "./index.js", "default": "./index.js"}, "./ad-banners": {"import": "./core-commons/ad-banners/index.js", "types": "./core-commons/ad-banners/index.d.ts", "svelte": "./core-commons/ad-banners/index.js", "default": "./core-commons/ad-banners/index.js"}, "./api-util": {"import": "./core-commons/api-util/index.js", "types": "./core-commons/api-util/index.d.ts", "svelte": "./core-commons/api-util/index.js", "default": "./core-commons/api-util/index.js"}, "./api-client": {"import": "./core-commons/api-clients/index.js", "types": "./core-commons/api-clients/index.d.ts", "svelte": "./core-commons/api-clients/index.js", "default": "./core-commons/api-clients/index.js"}, "./cart": {"import": "./core-commons/cart/index.js", "types": "./core-commons/cart/index.d.ts", "svelte": "./core-commons/cart/index.js", "default": "./core-commons/cart/index.js"}, "./cart/coupons": {"import": "./core-commons/cart/coupons/index.js", "types": "./core-commons/cart/coupons/index.d.ts", "svelte": "./core-commons/cart/coupons/index.js", "default": "./core-commons/cart/coupons/index.js"}, "./cart/rewards": {"import": "./core-commons/cart/rewards/index.js", "types": "./core-commons/cart/rewards/index.d.ts", "svelte": "./core-commons/cart/rewards/index.js", "default": "./core-commons/cart/rewards/index.js"}, "./cart/fare-details": {"import": "./core-commons/cart/fare-details/index.js", "types": "./core-commons/cart/fare-details/index.d.ts", "svelte": "./core-commons/cart/fare-details/index.js", "default": "./core-commons/cart/fare-details/index.js"}, "./cart/utils": {"import": "./core-commons/cart/utils/index.js", "types": "./core-commons/cart/utils/index.d.ts", "svelte": "./core-commons/cart/utils/index.js", "default": "./core-commons/cart/utils/index.js"}, "./cart/wallet": {"import": "./core-commons/cart/wallet/index.js", "types": "./core-commons/cart/wallet/index.d.ts", "svelte": "./core-commons/cart/wallet/index.js", "default": "./core-commons/cart/wallet/index.js"}, "./calendar": {"import": "./core-commons/calendar/index.js", "types": "./core-commons/calendar/index.d.ts", "svelte": "./core-commons/calendar/index.js", "default": "./core-commons/calendar/index.js"}, "./calendar-v2": {"import": "./core-commons/calendar-v2/index.js", "types": "./core-commons/calendar-v2/index.d.ts", "svelte": "./core-commons/calendar-v2/index.js", "default": "./core-commons/calendar-v2/index.js"}, "./components/bottom-sheet": {"import": "./core-commons/components/bottom-sheet/index.js", "types": "./core-commons/components/bottom-sheet/index.d.ts", "svelte": "./core-commons/components/bottom-sheet/index.js", "default": "./core-commons/components/bottom-sheet/index.js"}, "./components/item-attribute": {"import": "./core-commons/components/item-attribute/index.js", "types": "./core-commons/components/item-attribute/index.d.ts", "svelte": "./core-commons/components/item-attribute/index.js", "default": "./core-commons/components/item-attribute/index.js"}, "./components/appbar": {"import": "./core-commons/components/appbar/index.js", "types": "./core-commons/components/appbar/index.d.ts", "svelte": "./core-commons/components/appbar/index.js", "default": "./core-commons/components/appbar/index.js"}, "./components/clickable-div": {"import": "./core-commons/components/clickable-div/index.js", "types": "./core-commons/components/clickable-div/index.d.ts", "svelte": "./core-commons/components/clickable-div/index.js", "default": "./core-commons/components/clickable-div/index.js"}, "./components/confirmation-dialog": {"import": "./core-commons/components/confirmation-dialog/index.js", "types": "./core-commons/components/confirmation-dialog/index.d.ts", "svelte": "./core-commons/components/confirmation-dialog/index.js", "default": "./core-commons/components/confirmation-dialog/index.js"}, "./components/divider": {"import": "./core-commons/components/divider/index.js", "types": "./core-commons/components/divider/index.d.ts", "svelte": "./core-commons/components/divider/index.js", "default": "./core-commons/components/divider/index.js"}, "./components/error-view": {"import": "./core-commons/components/error-view/index.js", "types": "./core-commons/components/error-view/index.d.ts", "svelte": "./core-commons/components/error-view/index.js", "default": "./core-commons/components/error-view/index.js"}, "./components/icon-button": {"import": "./core-commons/components/icon-button/index.js", "types": "./core-commons/components/icon-button/index.d.ts", "svelte": "./core-commons/components/icon-button/index.js", "default": "./core-commons/components/icon-button/index.js"}, "./components/image": {"import": "./core-commons/components/image/index.js", "types": "./core-commons/components/image/index.d.ts", "svelte": "./core-commons/components/image/index.js", "default": "./core-commons/components/image/index.js"}, "./components/intersection-observer": {"import": "./core-commons/components/intersection-observer/index.js", "types": "./core-commons/components/intersection-observer/index.d.ts", "svelte": "./core-commons/components/intersection-observer/index.js", "default": "./core-commons/components/intersection-observer/index.js"}, "./components/list": {"import": "./core-commons/components/list/index.js", "types": "./core-commons/components/list/index.d.ts", "svelte": "./core-commons/components/list/index.js", "default": "./core-commons/components/list/index.js"}, "./components/lazy-load-list": {"import": "./core-commons/components/lazy-load-list/index.js", "types": "./core-commons/components/lazy-load-list/index.d.ts", "svelte": "./core-commons/components/lazy-load-list/index.js", "default": "./core-commons/components/lazy-load-list/index.js"}, "./components/loader": {"import": "./core-commons/components/loader/index.js", "types": "./core-commons/components/loader/index.d.ts", "svelte": "./core-commons/components/loader/index.js", "default": "./core-commons/components/loader/index.js"}, "./components/loader-dialog": {"import": "./core-commons/components/loader-dialog/index.js", "types": "./core-commons/components/loader-dialog/index.d.ts", "svelte": "./core-commons/components/loader-dialog/index.js", "default": "./core-commons/components/loader-dialog/index.js"}, "./components/powered-by-nuclei-footer": {"import": "./core-commons/components/powered-by-nuclei-footer/index.js", "types": "./core-commons/components/powered-by-nuclei-footer/index.d.ts", "svelte": "./core-commons/components/powered-by-nuclei-footer/index.js", "default": "./core-commons/components/powered-by-nuclei-footer/index.js"}, "./components/primary-button": {"import": "./core-commons/components/primary-button/index.js", "types": "./core-commons/components/primary-button/index.d.ts", "svelte": "./core-commons/components/primary-button/index.js", "default": "./core-commons/components/primary-button/index.js"}, "./components/primary-loader": {"import": "./core-commons/components/primary-loader/index.js", "types": "./core-commons/components/primary-loader/index.d.ts", "svelte": "./core-commons/components/primary-loader/index.js", "default": "./core-commons/components/primary-loader/index.js"}, "./components/radio-list": {"import": "./core-commons/components/radio-list/index.js", "types": "./core-commons/components/radio-list/index.d.ts", "svelte": "./core-commons/components/radio-list/index.js", "default": "./core-commons/components/radio-list/index.js"}, "./components/search-bar": {"import": "./core-commons/components/search-bar/index.js", "types": "./core-commons/components/search-bar/index.d.ts", "svelte": "./core-commons/components/search-bar/index.js", "default": "./core-commons/components/search-bar/index.js"}, "./components/secondary-button": {"import": "./core-commons/components/secondary-button/index.js", "types": "./core-commons/components/secondary-button/index.d.ts", "svelte": "./core-commons/components/secondary-button/index.js", "default": "./core-commons/components/secondary-button/index.js"}, "./components/segment-header": {"import": "./core-commons/components/segment-header-component/index.js", "types": "./core-commons/components/segment-header-component/index.d.ts", "svelte": "./core-commons/components/segment-header-component/index.js", "default": "./core-commons/components/segment-header-component/index.js"}, "./components/sold-by-vendor": {"import": "./core-commons/components/sold-by-vendor/index.js", "types": "./core-commons/components/sold-by-vendor/index.d.ts", "svelte": "./core-commons/components/sold-by-vendor/index.js", "default": "./core-commons/components/sold-by-vendor/index.js"}, "./components/tab": {"import": "./core-commons/components/tab/index.js", "types": "./core-commons/components/tab/index.d.ts", "svelte": "./core-commons/components/tab/index.js", "default": "./core-commons/components/tab/index.js"}, "./components/tab-headers": {"import": "./core-commons/components/tab-headers/index.js", "types": "./core-commons/components/tab-headers/index.d.ts", "svelte": "./core-commons/components/tab-headers/index.js", "default": "./core-commons/components/tab-headers/index.js"}, "./components/tabview": {"import": "./core-commons/components/tabview/index.js", "types": "./core-commons/components/tabview/index.d.ts", "svelte": "./core-commons/components/tabview/index.js", "default": "./core-commons/components/tabview/index.js"}, "./components/text-field-error": {"import": "./core-commons/components/text-field-error/index.js", "types": "./core-commons/components/text-field-error/index.d.ts", "svelte": "./core-commons/components/text-field-error/index.js", "default": "./core-commons/components/text-field-error/index.js"}, "./components/text-input-field": {"import": "./core-commons/components/text-input-field/index.js", "types": "./core-commons/components/text-input-field/index.d.ts", "svelte": "./core-commons/components/text-input-field/index.js", "default": "./core-commons/components/text-input-field/index.js"}, "./components/three-dot-menu": {"import": "./core-commons/components/three-dot-menu/index.js", "types": "./core-commons/components/three-dot-menu/index.d.ts", "svelte": "./core-commons/components/three-dot-menu/index.js", "default": "./core-commons/components/three-dot-menu/index.js"}, "./components/phone-input-field": {"import": "./core-commons/components/phone-input-field/index.js", "types": "./core-commons/components/phone-input-field/index.d.ts", "svelte": "./core-commons/components/phone-input-field/index.js", "default": "./core-commons/components/phone-input-field/index.js"}, "./components/stop-service": {"import": "./core-commons/components/stop-service/index.js", "types": "./core-commons/components/stop-service/index.d.ts", "svelte": "./core-commons/components/stop-service/index.js", "default": "./core-commons/components/stop-service/index.js"}, "./image-constants": {"import": "./core-commons/constants/image-constants.js", "types": "./core-commons/constants/image-constants.d.ts", "svelte": "./core-commons/constants/image-constants.js", "default": "./core-commons/constants/image-constants.js"}, "./constants": {"import": "./core-commons/constants/constants.js", "types": "./core-commons/constants/constants.d.ts", "svelte": "./core-commons/constants/constants.js", "default": "./core-commons/constants/constants.js"}, "./contact-card": {"import": "./core-commons/contact-card/index.js", "types": "./core-commons/contact-card/index.d.ts", "svelte": "./core-commons/contact-card/index.js", "default": "./core-commons/contact-card/index.js"}, "./country-list": {"import": "./core-commons/countrylist/index.js", "types": "./core-commons/countrylist/index.d.ts", "svelte": "./core-commons/countrylist/index.js", "default": "./core-commons/countrylist/index.js"}, "./error-handling": {"import": "./core-commons/error-handling/index.js", "types": "./core-commons/error-handling/index.d.ts", "svelte": "./core-commons/error-handling/index.js", "default": "./core-commons/error-handling/index.js"}, "./logger": {"import": "./core-commons/logger/nuclei-logger.js", "types": "./core-commons/logger/nuclei-logger.d.ts", "svelte": "./core-commons/logger/nuclei-logger.js", "default": "./core-commons/logger/nuclei-logger.js"}, "./messages": {"import": "./core-commons/messages/index.js", "types": "./core-commons/messages/index.d.ts", "svelte": "./core-commons/messages/index.js", "default": "./core-commons/messages/index.js"}, "./navigator": {"import": "./core-commons/navigator/index.js", "types": "./core-commons/navigator/index.d.ts", "svelte": "./core-commons/navigator/index.js", "default": "./core-commons/navigator/index.js"}, "./payment": {"import": "./core-commons/payment/nuclei-payment-data-parser.js", "types": "./core-commons/payment/nuclei-payment-data-parser.d.ts", "svelte": "./core-commons/payment/nuclei-payment-data-parser.js", "default": "./core-commons/payment/nuclei-payment-data-parser.js"}, "./routes-pages": {"import": "./core-commons/routes-pages/index.js", "types": "./core-commons/routes-pages/index.d.ts", "svelte": "./core-commons/routes-pages/index.js", "default": "./core-commons/routes-pages/index.js"}, "./universal-traveller": {"import": "./core-commons/universaltraveller/index.js", "types": "./core-commons/universaltraveller/index.d.ts", "svelte": "./core-commons/universaltraveller/index.js", "default": "./core-commons/universaltraveller/index.js"}, "./util": {"import": "./core-commons/util/index.js", "types": "./core-commons/util/index.d.ts", "svelte": "./core-commons/util/index.js", "default": "./core-commons/util/index.js"}, "./nubridge": {"import": "./core-libs/nubridge.js", "types": "./core-libs/nubridge.d.ts", "svelte": "./core-libs/nubridge.js", "default": "./core-libs/nubridge.js"}, "./url-util": {"import": "./core-libs/url.js", "types": "./core-libs/url.d.ts", "svelte": "./core-libs/url.js", "default": "./core-libs/url.js"}, "./theme-util": {"import": "./core-libs/theme-util.js", "types": "./core-libs/theme-util.d.ts", "svelte": "./core-libs/theme-util.js", "default": "./core-libs/theme-util.js"}, "./native-bridge-util": {"import": "./core-libs/native-bridge-util.js", "types": "./core-libs/native-bridge-util.d.ts", "svelte": "./core-libs/native-bridge-util.js", "default": "./core-libs/native-bridge-util.js"}, "./analytics": {"import": "./core-libs/analytics/index.js", "types": "./core-libs/analytics/index.d.ts", "svelte": "./core-libs/analytics/index.js", "default": "./core-libs/analytics/index.js"}, "./amount-formate-util": {"import": "./core-libs/amount-formate-util.js", "types": "./core-libs/amount-formate-util.d.ts", "svelte": "./core-libs/amount-formate-util.js", "default": "./core-libs/amount-formate-util.js"}, "./i18n": {"import": "./core-libs/i18n/i18n.js", "types": "./core-libs/i18n/i18n.d.ts", "svelte": "./core-libs/i18n/i18n.js", "default": "./core-libs/i18n/i18n.js"}, "./error-util": {"import": "./core-libs/error-util.js", "types": "./core-libs/error-util.d.ts", "svelte": "./core-libs/error-util.js", "default": "./core-libs/error-util.js"}, "./global-css": "./core-design/global.css", "./tailwind": "./core-design/design-system-preset.cjs"}, "devDependencies": {"@originjs/vite-plugin-commonjs": "^1.0.3", "@playwright/test": "1.25.0", "@sveltejs/adapter-auto": "1.0.0-next.80", "@sveltejs/adapter-node": "1.0.0-next.101", "@sveltejs/kit": "1.0.0-next.507", "@sveltejs/package": "^1.0.2", "@testing-library/jest-dom": "^5.16.5", "@testing-library/svelte": "^3.2.2", "@types/jest": "^29.2.0", "@vitest/coverage-c8": "^0.24.5", "autoprefixer": "^10.4.12", "c8": "^7.12.0", "eslint": "^8.16.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-svelte3": "^4.0.0", "jsdom": "^20.0.1", "postcss": "^8.4.17", "prettier": "^2.6.2", "prettier-plugin-svelte": "^2.7.0", "svelte-calendar": "^3.1.6", "svelte-check": "^2.7.1", "svelte-preprocess": "^4.10.7", "tailwindcss": "3.1.8", "tailwindcss-flip": "1.0.0", "typescript": "^4.7.4", "vite": "^3.1.0", "vitest": "^0.24.0"}, "dependencies": {"daisyui": "2.31.0", "dsbridge": "^3.1.4", "grpc-web": "^1.4.1", "hex-to-hsl": "^1.0.0", "log-symbols": "^5.1.0", "platform": "^1.3.6", "svelte": "^3.44.0"}, "type": "module"}