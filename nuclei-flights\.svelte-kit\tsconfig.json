{"compilerOptions": {"baseUrl": "..", "paths": {"$flights": ["src/routes/flights"], "$flights/*": ["src/routes/flights/*"], "$lib": ["src/lib"], "$lib/*": ["src/lib/*"]}, "rootDirs": ["..", "./types"], "importsNotUsedAsValues": "error", "isolatedModules": true, "preserveValueImports": true, "lib": ["esnext", "DOM", "DOM.Iterable"], "moduleResolution": "node", "module": "esnext", "target": "esnext"}, "include": ["ambient.d.ts", "../vite.config.ts", "../src/**/*.js", "../src/**/*.ts", "../src/**/*.svelte", "../src/**/*.js", "../src/**/*.ts", "../src/**/*.svelte", "../tests/**/*.js", "../tests/**/*.ts", "../tests/**/*.svelte"], "exclude": ["../node_modules/**", "./[!ambient.d.ts]**"]}